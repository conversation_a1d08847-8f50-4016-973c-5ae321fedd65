// frontend/src/app/checkout/page.tsx
'use client'; // Required for client-side components in Next.js

export default function CheckoutPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Checkout</h1>
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
        <p>Payment processing is currently unavailable. Stripe integration has been removed.</p>
        <p>Please implement alternative payment processing.</p>
      </div>
    </div>
  );
}